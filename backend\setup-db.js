const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Setting up database...');

try {
  // Create SQLite database file if it doesn't exist
  const dbPath = path.join(__dirname, 'dev.db');
  if (!fs.existsSync(dbPath)) {
    fs.writeFileSync(dbPath, '');
    console.log('Created SQLite database file');
  }

  // Generate Prisma client
  console.log('Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });

  // Run migrations
  console.log('Running database migrations...');
  execSync('npx prisma migrate dev --name init', { stdio: 'inherit' });

  console.log('Database setup complete!');
} catch (error) {
  console.error('Database setup failed:', error.message);
  process.exit(1);
}
