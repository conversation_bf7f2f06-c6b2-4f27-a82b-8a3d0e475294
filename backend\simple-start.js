const express = require('express');
const cors = require('cors');
const path = require('path');

// Simple Express server without Prisma for testing
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// Simple health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'TC Platform API is running (simple mode)'
  });
});

// Simple auth endpoint for testing
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Simple mock authentication
  if (email && password) {
    res.json({
      success: true,
      token: 'mock-jwt-token',
      user: {
        id: '1',
        email: email,
        firstName: 'Test',
        lastName: 'User',
        role: 'TC'
      }
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'Email and password required'
    });
  }
});

// Simple users endpoint
app.get('/api/users/me', (req, res) => {
  res.json({
    id: '1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    role: 'TC'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'TC Platform API (Simple Mode)',
    version: '1.0.0',
    status: 'running'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 TC Platform API (Simple Mode) running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 Frontend should connect to: http://localhost:${PORT}`);
});
